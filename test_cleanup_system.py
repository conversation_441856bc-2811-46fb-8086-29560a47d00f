#!/usr/bin/env python3
"""
Test script for the cleanup system
Demonstrates that files are NOT stored on disk and cleanup system works
"""

import requests
import time
import os
from pathlib import Path
import json

def test_cleanup_system():
    """Test the cleanup system functionality"""
    base_url = "http://localhost:8000"
    
    print("🧹 Testing Cleanup System")
    print("=" * 50)
    
    # 1. Check initial cleanup status
    print("\n1️⃣ Initial Cleanup Status...")
    try:
        response = requests.get(f"{base_url}/cleanup-status")
        status = response.json()
        
        print(f"   Cleanup Enabled: {status['cleanup_enabled']}")
        print(f"   Cleanup Interval: {status['cleanup_interval']}s")
        print(f"   Max File Age: {status['max_file_age_hours']} hours")
        print(f"   Uploads Folder: {status['uploads_folder']}")
        print(f"   In-Memory Processing: {status['in_memory_processing']}")
        print(f"   Current Files: {status['uploads_info']['file_count']}")
        print(f"   Total Size: {status['uploads_info']['total_size']} bytes")
        print(f"   Note: {status['note']}")
        
    except Exception as e:
        print(f"   ❌ Failed to get cleanup status: {e}")
        return
    
    # 2. Verify in-memory processing
    print("\n2️⃣ Verifying In-Memory Processing...")
    uploads_folder = Path("uploads")
    
    # Count files before any API calls
    files_before = 0
    if uploads_folder.exists():
        files_before = len([f for f in uploads_folder.glob("*") if f.is_file()])
    
    print(f"   Files in uploads folder before API call: {files_before}")
    
    # Make API call with dummy audio file (if exists)
    test_audio = "test_audio.wav"
    if Path(test_audio).exists():
        print(f"   Making API call with {test_audio}...")
        try:
            with open(test_audio, 'rb') as f:
                files = {'file': f}
                response = requests.post(f"{base_url}/identify", files=files)
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"   ✅ API call successful: US confidence = {result['us_confidence']}%")
                else:
                    print(f"   ❌ API call failed: {response.status_code}")
                    
        except Exception as e:
            print(f"   ❌ API call error: {e}")
    else:
        print(f"   ⚠️ No {test_audio} found, skipping API test")
    
    # Count files after API call
    files_after = 0
    if uploads_folder.exists():
        files_after = len([f for f in uploads_folder.glob("*") if f.is_file()])
    
    print(f"   Files in uploads folder after API call: {files_after}")
    
    if files_after == files_before:
        print("   ✅ CONFIRMED: No files stored on disk during API call!")
    else:
        print("   ⚠️ WARNING: File count changed, investigating...")
    
    # 3. Create test files for cleanup demonstration
    print("\n3️⃣ Creating Test Files for Cleanup Demo...")
    
    # Ensure uploads folder exists
    uploads_folder.mkdir(exist_ok=True)
    
    # Create test files
    test_files = []
    for i in range(3):
        test_file = uploads_folder / f"test_file_{i}.txt"
        test_file.write_text(f"Test content {i}")
        test_files.append(test_file)
        print(f"   Created: {test_file.name}")
    
    # Check status after creating files
    response = requests.get(f"{base_url}/cleanup-status")
    status = response.json()
    print(f"   Files after creation: {status['uploads_info']['file_count']}")
    print(f"   Total size: {status['uploads_info']['total_size']} bytes")
    
    # 4. Test manual cleanup
    print("\n4️⃣ Testing Manual Cleanup...")
    
    # First, let's check if files are old enough to be cleaned
    # (by default, files need to be older than 1 hour)
    print("   Note: Files need to be older than 1 hour by default")
    print("   For demo purposes, let's trigger cleanup anyway...")
    
    try:
        response = requests.post(f"{base_url}/cleanup-files")
        cleanup_result = response.json()
        
        print(f"   Cleanup Message: {cleanup_result['message']}")
        print(f"   Files Deleted: {cleanup_result['files_deleted']}")
        print(f"   Bytes Freed: {cleanup_result['bytes_freed']}")
        
        if cleanup_result['files_deleted'] == 0:
            print("   💡 No files deleted (they're not old enough)")
            print("   💡 This is expected behavior - files must be older than 1 hour")
        
    except Exception as e:
        print(f"   ❌ Manual cleanup failed: {e}")
    
    # 5. Check final status
    print("\n5️⃣ Final Status Check...")
    try:
        response = requests.get(f"{base_url}/cleanup-status")
        status = response.json()
        
        print(f"   Final file count: {status['uploads_info']['file_count']}")
        print(f"   Total cleanups performed: {status['cleanup_stats']['total_cleanups']}")
        print(f"   Total files deleted: {status['cleanup_stats']['files_deleted']}")
        print(f"   Total bytes freed: {status['cleanup_stats']['bytes_freed']}")
        
        if status['cleanup_stats']['last_cleanup']:
            print(f"   Last cleanup: {status['cleanup_stats']['last_cleanup']}")
        
    except Exception as e:
        print(f"   ❌ Failed to get final status: {e}")
    
    # 6. Cleanup test files manually (since they're too new for auto-cleanup)
    print("\n6️⃣ Manual Test File Cleanup...")
    deleted_count = 0
    for test_file in test_files:
        if test_file.exists():
            try:
                test_file.unlink()
                deleted_count += 1
                print(f"   Deleted: {test_file.name}")
            except Exception as e:
                print(f"   Failed to delete {test_file.name}: {e}")
    
    print(f"   Manually deleted {deleted_count} test files")

def test_api_stats():
    """Test API stats with cleanup information"""
    print("\n" + "=" * 50)
    print("📊 API Statistics with Cleanup Info")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    try:
        response = requests.get(f"{base_url}/stats")
        stats = response.json()
        
        print(f"\n📈 Performance Stats:")
        print(f"   Total Requests: {stats.get('total_requests', 0)}")
        print(f"   Average Time: {stats.get('average_inference_time', 0):.3f}s")
        print(f"   Cache Hit Rate: {stats.get('cache_hit_rate', 0):.1f}%")
        print(f"   Memory Usage: {stats.get('memory_usage_mb', 0):.1f} MB")
        
        print(f"\n🚦 Queue Stats:")
        print(f"   Queue Size: {stats.get('queue_size', 0)}/{stats.get('max_queue_size', 0)}")
        print(f"   Active Requests: {stats.get('active_requests', 0)}/{stats.get('max_concurrent_requests', 0)}")
        print(f"   Available Slots: {stats.get('available_slots', 0)}")
        
        print(f"\n🧹 Cleanup Stats:")
        print(f"   Cleanup Enabled: {stats.get('cleanup_enabled', False)}")
        cleanup_stats = stats.get('cleanup_stats', {})
        print(f"   Total Cleanups: {cleanup_stats.get('total_cleanups', 0)}")
        print(f"   Files Deleted: {cleanup_stats.get('files_deleted', 0)}")
        print(f"   Bytes Freed: {cleanup_stats.get('bytes_freed', 0)}")
        
        if cleanup_stats.get('last_cleanup'):
            print(f"   Last Cleanup: {cleanup_stats['last_cleanup']}")
        
    except Exception as e:
        print(f"❌ Failed to get stats: {e}")

def demonstrate_in_memory_processing():
    """Demonstrate that files are processed in memory"""
    print("\n" + "=" * 50)
    print("💾 In-Memory Processing Demonstration")
    print("=" * 50)
    
    print("\n🔍 Key Points:")
    print("   1. Files are read directly into memory using await file.read()")
    print("   2. Audio processing uses io.BytesIO() for in-memory operations")
    print("   3. No temporary files are created on disk")
    print("   4. Memory is automatically freed after processing")
    print("   5. Cleanup system is preventive - cleans any residual files")
    
    print("\n📝 Code Flow:")
    print("   Client Upload → file_content = await file.read()")
    print("   Memory Buffer → audio_buffer = io.BytesIO(file_content)")
    print("   Audio Load   → torchaudio.load(audio_buffer, format='wav')")
    print("   Processing   → model inference in memory")
    print("   Response     → return JSON result")
    print("   Cleanup      → memory automatically freed")
    
    print("\n✅ Benefits:")
    print("   • Faster processing (no disk I/O)")
    print("   • Better security (no files on disk)")
    print("   • No disk space issues")
    print("   • Automatic memory management")
    print("   • Scalable for high traffic")

if __name__ == "__main__":
    print("🧹 Cleanup System Test Suite")
    
    try:
        # Test cleanup system
        test_cleanup_system()
        
        # Test API stats
        test_api_stats()
        
        # Demonstrate in-memory processing
        demonstrate_in_memory_processing()
        
        print("\n✅ All cleanup tests completed!")
        print("\n💡 Key Takeaway: Files are processed in-memory, NOT stored on disk!")
        
    except KeyboardInterrupt:
        print("\n❌ Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
