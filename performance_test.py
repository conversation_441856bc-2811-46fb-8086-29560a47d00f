#!/usr/bin/env python3
"""
Performance test script for Accent Recognition API
Tests the optimized API performance with various scenarios
"""

import asyncio
import aiohttp
import time
import statistics
import os
import wave
import numpy as np
from typing import List, Dict, Any
import argparse
import json

class PerformanceTester:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def create_test_audio(self, duration: float = 3.0, sample_rate: int = 16000, filename: str = "test_audio.wav") -> str:
        """Create a test WAV file"""
        # Generate sine wave audio
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        frequency = 440  # A4 note
        audio_data = np.sin(2 * np.pi * frequency * t)
        
        # Convert to 16-bit PCM
        audio_data = (audio_data * 32767).astype(np.int16)
        
        # Write WAV file
        with wave.open(filename, 'w') as wav_file:
            wav_file.setnchannels(1)  # Mono
            wav_file.setsampwidth(2)  # 16-bit
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_data.tobytes())
        
        return filename
    
    async def test_health_check(self) -> Dict[str, Any]:
        """Test health check endpoint"""
        async with self.session.get(f"{self.base_url}/health") as response:
            return await response.json()
    
    async def test_single_request(self, audio_file: str) -> Dict[str, Any]:
        """Test single accent identification request"""
        start_time = time.time()
        
        with open(audio_file, 'rb') as f:
            data = aiohttp.FormData()
            data.add_field('file', f, filename=os.path.basename(audio_file), content_type='audio/wav')
            
            async with self.session.post(f"{self.base_url}/identify", data=data) as response:
                result = await response.json()
                
        end_time = time.time()
        
        return {
            "response": result,
            "response_time": end_time - start_time,
            "status_code": response.status
        }
    
    async def test_concurrent_requests(self, audio_file: str, num_requests: int = 10) -> List[Dict[str, Any]]:
        """Test concurrent requests"""
        tasks = []
        for _ in range(num_requests):
            task = asyncio.create_task(self.test_single_request(audio_file))
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        return [r for r in results if not isinstance(r, Exception)]
    
    async def test_cache_performance(self, audio_file: str, num_requests: int = 5) -> Dict[str, Any]:
        """Test cache performance with same file"""
        results = []
        
        # First request (cache miss)
        first_result = await self.test_single_request(audio_file)
        results.append(first_result)
        
        # Subsequent requests (cache hits)
        for _ in range(num_requests - 1):
            result = await self.test_single_request(audio_file)
            results.append(result)
        
        return {
            "first_request_time": results[0]["response_time"],
            "cached_request_times": [r["response_time"] for r in results[1:]],
            "cache_speedup": results[0]["response_time"] / statistics.mean([r["response_time"] for r in results[1:]]) if len(results) > 1 else 1.0
        }
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get API performance statistics"""
        async with self.session.get(f"{self.base_url}/stats") as response:
            return await response.json()
    
    async def clear_cache(self) -> Dict[str, Any]:
        """Clear API cache"""
        async with self.session.post(f"{self.base_url}/clear-cache") as response:
            return await response.json()

async def run_performance_tests(base_url: str = "http://localhost:8000", num_concurrent: int = 10, num_cache_tests: int = 5):
    """Run comprehensive performance tests"""
    print("🚀 Starting Performance Tests for Accent Recognition API")
    print("=" * 60)
    
    async with PerformanceTester(base_url) as tester:
        # Create test audio file
        print("📁 Creating test audio file...")
        test_audio = tester.create_test_audio(duration=3.0)
        
        try:
            # Test 1: Health Check
            print("\n1️⃣ Testing Health Check...")
            health = await tester.test_health_check()
            print(f"   ✅ Health Status: {health.get('status', 'unknown')}")
            print(f"   🔧 Device: {health.get('device', 'unknown')}")
            print(f"   🧠 Model Loaded: {health.get('model_loaded', False)}")
            
            # Clear cache before tests
            await tester.clear_cache()
            
            # Test 2: Single Request Performance
            print("\n2️⃣ Testing Single Request Performance...")
            single_result = await tester.test_single_request(test_audio)
            print(f"   ⏱️ Response Time: {single_result['response_time']:.3f}s")
            print(f"   🎯 Predicted Accent: {single_result['response']['predicted_accent']}")
            print(f"   🇺🇸 US Confidence: {single_result['response']['us_confidence']}%")
            
            # Test 3: Cache Performance
            print(f"\n3️⃣ Testing Cache Performance ({num_cache_tests} requests)...")
            cache_result = await tester.test_cache_performance(test_audio, num_cache_tests)
            print(f"   🐌 First Request (Cache Miss): {cache_result['first_request_time']:.3f}s")
            if cache_result['cached_request_times']:
                avg_cached = statistics.mean(cache_result['cached_request_times'])
                print(f"   ⚡ Cached Requests (Avg): {avg_cached:.3f}s")
                print(f"   📈 Cache Speedup: {cache_result['cache_speedup']:.2f}x")
            
            # Test 4: Concurrent Requests
            print(f"\n4️⃣ Testing Concurrent Requests ({num_concurrent} requests)...")
            start_time = time.time()
            concurrent_results = await tester.test_concurrent_requests(test_audio, num_concurrent)
            total_time = time.time() - start_time
            
            if concurrent_results:
                response_times = [r['response_time'] for r in concurrent_results]
                print(f"   📊 Total Time: {total_time:.3f}s")
                print(f"   📊 Successful Requests: {len(concurrent_results)}/{num_concurrent}")
                print(f"   📊 Average Response Time: {statistics.mean(response_times):.3f}s")
                print(f"   📊 Min Response Time: {min(response_times):.3f}s")
                print(f"   📊 Max Response Time: {max(response_times):.3f}s")
                print(f"   📊 Requests/Second: {len(concurrent_results)/total_time:.2f}")
            
            # Test 5: Final Statistics
            print("\n5️⃣ Final API Statistics...")
            final_stats = await tester.get_stats()
            print(f"   📈 Total Requests: {final_stats.get('total_requests', 0)}")
            print(f"   ⏱️ Average Inference Time: {final_stats.get('average_inference_time', 0):.3f}s")
            print(f"   💾 Cache Hit Rate: {final_stats.get('cache_hit_rate', 0):.1f}%")
            print(f"   🧠 Memory Usage: {final_stats.get('memory_usage_mb', 0):.1f} MB")
            print(f"   💾 Cache Size: {final_stats.get('cache_size', 0)}/{final_stats.get('max_cache_size', 0)}")
            
        finally:
            # Cleanup
            if os.path.exists(test_audio):
                os.remove(test_audio)
                print(f"\n🧹 Cleaned up test file: {test_audio}")
    
    print("\n✅ Performance tests completed!")

def main():
    parser = argparse.ArgumentParser(description="Performance test for Accent Recognition API")
    parser.add_argument("--url", default="http://localhost:8000", help="API base URL")
    parser.add_argument("--concurrent", type=int, default=10, help="Number of concurrent requests")
    parser.add_argument("--cache-tests", type=int, default=5, help="Number of cache test requests")
    
    args = parser.parse_args()
    
    try:
        asyncio.run(run_performance_tests(args.url, args.concurrent, args.cache_tests))
    except KeyboardInterrupt:
        print("\n❌ Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")

if __name__ == "__main__":
    main()
