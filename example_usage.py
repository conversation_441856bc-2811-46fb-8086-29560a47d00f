#!/usr/bin/env python3
"""
Example usage of the optimized Accent Recognition API
Demonstrates the new response format and queue system
"""

import requests
import time
import json
from pathlib import Path

def test_api_basic():
    """Basic API test with example audio file"""
    base_url = "http://localhost:8000"
    
    print("🎵 Testing Accent Recognition API")
    print("=" * 50)
    
    # 1. Health Check
    print("\n1️⃣ Health Check...")
    try:
        response = requests.get(f"{base_url}/health")
        health = response.json()
        print(f"   Status: {health['status']}")
        print(f"   Device: {health['device']}")
        print(f"   Model Loaded: {health['model_loaded']}")
    except Exception as e:
        print(f"   ❌ Health check failed: {e}")
        return
    
    # 2. API Information
    print("\n2️⃣ API Information...")
    try:
        response = requests.get(f"{base_url}/")
        info = response.json()
        print(f"   Version: {info['version']}")
        print(f"   Response Format: {info['response_format']['identify_endpoint']['description']}")
        print(f"   Example Response: {info['response_format']['identify_endpoint']['example']}")
        print(f"   Max Concurrent: {info['queue_system']['max_concurrent_requests']}")
        print(f"   Max Queue Size: {info['queue_system']['max_queue_size']}")
    except Exception as e:
        print(f"   ❌ API info failed: {e}")
    
    # 3. Test with audio file (you need to provide your own audio file)
    audio_file = "test_audio.wav"  # Replace with your audio file
    
    if Path(audio_file).exists():
        print(f"\n3️⃣ Testing Accent Recognition with {audio_file}...")
        
        try:
            with open(audio_file, 'rb') as f:
                files = {'file': f}
                start_time = time.time()
                response = requests.post(f"{base_url}/identify", files=files)
                end_time = time.time()
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"   ✅ Success!")
                    print(f"   🇺🇸 US Confidence: {result['us_confidence']}%")
                    print(f"   ⏱️ Response Time: {end_time - start_time:.3f}s")
                    
                    # Test again for cache hit
                    print(f"\n   🔄 Testing cache (same file)...")
                    with open(audio_file, 'rb') as f2:
                        files2 = {'file': f2}
                        start_time2 = time.time()
                        response2 = requests.post(f"{base_url}/identify", files=files2)
                        end_time2 = time.time()
                        
                        if response2.status_code == 200:
                            result2 = response2.json()
                            print(f"   ⚡ Cache Hit!")
                            print(f"   🇺🇸 US Confidence: {result2['us_confidence']}%")
                            print(f"   ⏱️ Response Time: {end_time2 - start_time2:.3f}s")
                            speedup = (end_time - start_time) / (end_time2 - start_time2)
                            print(f"   📈 Cache Speedup: {speedup:.1f}x")
                        
                else:
                    print(f"   ❌ Request failed: {response.status_code}")
                    print(f"   Error: {response.text}")
                    
        except Exception as e:
            print(f"   ❌ Test failed: {e}")
    else:
        print(f"\n3️⃣ Skipping audio test (no {audio_file} found)")
        print("   💡 To test with audio, place a .wav file named 'test_audio.wav' in this directory")
    
    # 4. Performance Statistics
    print(f"\n4️⃣ Performance Statistics...")
    try:
        response = requests.get(f"{base_url}/stats")
        stats = response.json()
        print(f"   📊 Total Requests: {stats['total_requests']}")
        print(f"   ⏱️ Average Time: {stats['average_inference_time']}s")
        print(f"   💾 Cache Hit Rate: {stats['cache_hit_rate']}%")
        print(f"   🧠 Memory Usage: {stats['memory_usage_mb']} MB")
        print(f"   📦 Cache Size: {stats['cache_size']}/{stats['max_cache_size']}")
        print(f"   🚦 Queue Size: {stats['queue_size']}/{stats['max_queue_size']}")
        print(f"   🔄 Active Requests: {stats['active_requests']}/{stats['max_concurrent_requests']}")
    except Exception as e:
        print(f"   ❌ Stats failed: {e}")
    
    # 5. Queue Status
    print(f"\n5️⃣ Queue Status...")
    try:
        response = requests.get(f"{base_url}/queue-status")
        queue_status = response.json()
        print(f"   🚦 Queue Size: {queue_status['queue_size']}")
        print(f"   🔄 Active Requests: {queue_status['active_requests']}")
        print(f"   🆓 Available Slots: {queue_status['available_slots']}")
        print(f"   📊 Queue Full: {queue_status['queue_full']}")
        print(f"   🔒 Semaphore Locked: {queue_status['semaphore_locked']}")
    except Exception as e:
        print(f"   ❌ Queue status failed: {e}")

def test_response_format():
    """Test and demonstrate the new response format"""
    print("\n" + "=" * 50)
    print("📋 Response Format Examples")
    print("=" * 50)
    
    print("\n✅ Successful Response:")
    print(json.dumps({"us_confidence": 85.67}, indent=2))
    
    print("\n🔄 No US Accent Detected:")
    print(json.dumps({"us_confidence": 0.0}, indent=2))
    
    print("\n❌ Error Responses:")
    print("400 Bad Request - Invalid file format:")
    print(json.dumps({"detail": "Only .wav files are allowed"}, indent=2))
    
    print("\n503 Service Unavailable - Server too busy:")
    print(json.dumps({"detail": "Server too busy. Please try again later."}, indent=2))
    
    print("\n408 Request Timeout - Queue timeout:")
    print(json.dumps({"detail": "Request timeout"}, indent=2))

def test_concurrent_requests():
    """Test concurrent requests to demonstrate queue system"""
    import threading
    import concurrent.futures
    
    print("\n" + "=" * 50)
    print("🚀 Testing Concurrent Requests")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    audio_file = "test_audio.wav"
    
    if not Path(audio_file).exists():
        print("❌ No test audio file found. Skipping concurrent test.")
        return
    
    def make_request(request_id):
        """Make a single request"""
        try:
            with open(audio_file, 'rb') as f:
                files = {'file': f}
                start_time = time.time()
                response = requests.post(f"{base_url}/identify", files=files, timeout=35)
                end_time = time.time()
                
                return {
                    'request_id': request_id,
                    'status_code': response.status_code,
                    'response_time': end_time - start_time,
                    'result': response.json() if response.status_code == 200 else response.text
                }
        except Exception as e:
            return {
                'request_id': request_id,
                'status_code': 'ERROR',
                'response_time': 0,
                'result': str(e)
            }
    
    # Send 10 concurrent requests
    num_requests = 10
    print(f"🔄 Sending {num_requests} concurrent requests...")
    
    start_time = time.time()
    with concurrent.futures.ThreadPoolExecutor(max_workers=num_requests) as executor:
        futures = [executor.submit(make_request, i) for i in range(num_requests)]
        results = [future.result() for future in concurrent.futures.as_completed(futures)]
    
    total_time = time.time() - start_time
    
    # Analyze results
    successful = [r for r in results if r['status_code'] == 200]
    failed = [r for r in results if r['status_code'] != 200]
    
    print(f"\n📊 Results:")
    print(f"   ✅ Successful: {len(successful)}/{num_requests}")
    print(f"   ❌ Failed: {len(failed)}/{num_requests}")
    print(f"   ⏱️ Total Time: {total_time:.3f}s")
    
    if successful:
        response_times = [r['response_time'] for r in successful]
        print(f"   📊 Avg Response Time: {sum(response_times)/len(response_times):.3f}s")
        print(f"   📊 Min Response Time: {min(response_times):.3f}s")
        print(f"   📊 Max Response Time: {max(response_times):.3f}s")
    
    if failed:
        print(f"\n❌ Failed Requests:")
        for r in failed:
            print(f"   Request {r['request_id']}: {r['status_code']} - {r['result']}")

if __name__ == "__main__":
    print("🎵 Accent Recognition API - Example Usage")
    
    try:
        # Basic API test
        test_api_basic()
        
        # Response format examples
        test_response_format()
        
        # Concurrent requests test
        test_concurrent_requests()
        
        print("\n✅ All tests completed!")
        
    except KeyboardInterrupt:
        print("\n❌ Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
