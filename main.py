from fastapi import Fast<PERSON><PERSON>, File, UploadFile, HTTPException
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import os
import shutil
import torch
import torchaudio
from speechbrain.pretrained.interfaces import foreign_class
import asyncio
import time
import hashlib
import io
from functools import lru_cache
from typing import Optional, <PERSON>ple
import logging
import psutil
import gc

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Inisialisasi FastAPI
app = FastAPI(title="Accent Recognition API")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configuration
class Config:
    MODEL_SOURCE = "Jzuluaga/accent-id-commonaccent_xlsr-en-english"
    PYMODULE_FILE = "custom_interface.py"
    CLASSNAME = "CustomEncoderWav2vec2Classifier"
    CACHE_SIZE = 100  # Number of results to cache
    MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
    DEVICE = "cuda" if torch.cuda.is_available() else "cpu"

config = Config()

# Global variables for model and cache
classifier = None
result_cache = {}

# Performance monitoring
class PerformanceMonitor:
    def __init__(self):
        self.request_count = 0
        self.total_inference_time = 0.0
        self.cache_hits = 0

    def log_request(self, inference_time: float, cache_hit: bool = False):
        self.request_count += 1
        self.total_inference_time += inference_time
        if cache_hit:
            self.cache_hits += 1

    def get_stats(self):
        avg_time = self.total_inference_time / max(self.request_count, 1)
        cache_hit_rate = self.cache_hits / max(self.request_count, 1) * 100
        return {
            "total_requests": self.request_count,
            "average_inference_time": round(avg_time, 3),
            "cache_hit_rate": round(cache_hit_rate, 2),
            "memory_usage_mb": round(psutil.Process().memory_info().rss / 1024 / 1024, 2)
        }

monitor = PerformanceMonitor()

def load_and_optimize_model():
    """Load model with optimizations"""
    global classifier

    logger.info(f"Loading model on device: {config.DEVICE}")
    start_time = time.time()

    try:
        classifier = foreign_class(
            source=config.MODEL_SOURCE,
            pymodule_file=config.PYMODULE_FILE,
            classname=config.CLASSNAME
        )

        # Move model to appropriate device
        if hasattr(classifier, 'to'):
            classifier = classifier.to(config.DEVICE)

        # Enable optimizations for PyTorch 2.0+
        if hasattr(torch, 'compile') and torch.__version__ >= "2.0":
            try:
                # Compile the model for faster inference
                if hasattr(classifier, 'mods'):
                    if hasattr(classifier.mods, 'wav2vec2'):
                        classifier.mods.wav2vec2 = torch.compile(classifier.mods.wav2vec2)
                    if hasattr(classifier.mods, 'output_mlp'):
                        classifier.mods.output_mlp = torch.compile(classifier.mods.output_mlp)
                logger.info("Model compiled successfully")
            except Exception as e:
                logger.warning(f"Model compilation failed: {e}")

        # Set model to evaluation mode
        classifier.eval()

        # Warm up the model with a dummy input
        try:
            dummy_audio = torch.randn(1, 16000).to(config.DEVICE)
            with torch.no_grad():
                _ = classifier.encode_batch(dummy_audio)
            logger.info("Model warmed up successfully")
        except Exception as e:
            logger.warning(f"Model warmup failed: {e}")

        load_time = time.time() - start_time
        logger.info(f"Model loaded and optimized in {load_time:.2f} seconds")

    except Exception as e:
        logger.error(f"Model loading failed: {e}")
        raise RuntimeError(f"Model loading failed: {e}")

# Load model saat aplikasi start-up
load_and_optimize_model()

def calculate_file_hash(file_content: bytes) -> str:
    """Calculate MD5 hash of file content for caching"""
    return hashlib.md5(file_content).hexdigest()

@lru_cache(maxsize=config.CACHE_SIZE)
def cached_inference(file_hash: str, file_content_bytes: bytes) -> Tuple[torch.Tensor, str]:
    """Cached inference function"""
    # Convert bytes back to audio tensor
    audio_buffer = io.BytesIO(file_content_bytes)

    # Load audio directly from memory
    waveform, sample_rate = torchaudio.load(audio_buffer, format="wav")

    # Ensure correct sample rate (16kHz for wav2vec2)
    if sample_rate != 16000:
        resampler = torchaudio.transforms.Resample(sample_rate, 16000)
        waveform = resampler(waveform)

    # Move to device
    waveform = waveform.to(config.DEVICE)

    # Perform inference
    with torch.no_grad():
        # Use encode_batch and output_mlp directly for better performance
        outputs = classifier.encode_batch(waveform)
        outputs = classifier.mods.output_mlp(outputs).squeeze(1)
        out_prob = classifier.hparams.softmax(outputs)

        # Get predicted label
        score, index = torch.max(out_prob, dim=-1)
        text_lab = classifier.hparams.label_encoder.decode_torch(index)

    return out_prob, text_lab

def get_us_confidence(out_prob: torch.Tensor) -> Optional[float]:
    """Extract US confidence score from probabilities"""
    try:
        labels = classifier.label_encoder.decode_ndim(torch.arange(len(out_prob)))
        us_index = None
        for i, label in enumerate(labels):
            if label.lower() == "us":
                us_index = i
                break

        if us_index is not None:
            us_confidence = float(out_prob[us_index])
            return round(us_confidence * 100, 2)  # Convert to percentage
        return None
    except Exception as e:
        logger.warning(f"Failed to extract US confidence: {e}")
        return None

@app.post("/identify")
async def identify_accent(file: UploadFile = File(...)):
    start_time = time.time()

    try:
        # Validasi jenis file
        if not file.filename.lower().endswith(".wav"):
            raise HTTPException(status_code=400, detail="Only .wav files are allowed")

        # Validasi ukuran file
        file_content = await file.read()
        if len(file_content) > config.MAX_FILE_SIZE:
            raise HTTPException(status_code=400, detail="File too large")

        if len(file_content) == 0:
            raise HTTPException(status_code=400, detail="Empty file")

        # Calculate file hash for caching
        file_hash = calculate_file_hash(file_content)

        # Check cache first
        cache_hit = file_hash in result_cache
        if cache_hit:
            result = result_cache[file_hash]
            inference_time = time.time() - start_time
            monitor.log_request(inference_time, cache_hit=True)

            logger.info(f"Cache hit for file hash: {file_hash[:8]}...")
            return JSONResponse(content=result)

        # Perform inference
        try:
            out_prob, text_lab = cached_inference(file_hash, file_content)
            us_confidence = get_us_confidence(out_prob)

            result = {
                "predicted_accent": text_lab,
                "us_confidence": us_confidence
            }

            # Cache the result
            if len(result_cache) >= config.CACHE_SIZE:
                # Remove oldest entry (simple FIFO)
                oldest_key = next(iter(result_cache))
                del result_cache[oldest_key]

            result_cache[file_hash] = result

            inference_time = time.time() - start_time
            monitor.log_request(inference_time, cache_hit=False)

            logger.info(f"Inference completed in {inference_time:.3f}s for file: {file.filename}")
            return JSONResponse(content=result)

        except Exception as e:
            logger.error(f"Inference error: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Inference error: {str(e)}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

    finally:
        # Force garbage collection to free memory
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        gc.collect()

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "model_loaded": classifier is not None,
        "device": config.DEVICE,
        "torch_version": torch.__version__
    }

@app.get("/stats")
async def get_performance_stats():
    """Get performance statistics"""
    stats = monitor.get_stats()
    stats.update({
        "cache_size": len(result_cache),
        "max_cache_size": config.CACHE_SIZE,
        "device": config.DEVICE
    })
    return stats

@app.post("/clear-cache")
async def clear_cache():
    """Clear the result cache"""
    global result_cache
    cache_size = len(result_cache)
    result_cache.clear()

    # Clear LRU cache
    cached_inference.cache_clear()

    # Force garbage collection
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    gc.collect()

    return {
        "message": f"Cache cleared. Removed {cache_size} entries.",
        "cache_info": cached_inference.cache_info()._asdict()
    }

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Accent Recognition API",
        "version": "2.0.0",
        "optimizations": [
            "In-memory file processing",
            "Result caching",
            "Model compilation (if supported)",
            "GPU acceleration (if available)",
            "Performance monitoring"
        ],
        "endpoints": {
            "/identify": "POST - Identify accent from audio file",
            "/health": "GET - Health check",
            "/stats": "GET - Performance statistics",
            "/clear-cache": "POST - Clear cache",
            "/": "GET - API information"
        }
    }