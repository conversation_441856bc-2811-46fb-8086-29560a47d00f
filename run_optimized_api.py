#!/usr/bin/env python3
"""
Startup script for the optimized Accent Recognition API
Includes automatic dependency installation and performance monitoring
"""

import subprocess
import sys
import os
import time
import requests
from pathlib import Path

def check_and_install_dependencies():
    """Check and install required dependencies"""
    print("🔍 Checking dependencies...")
    
    try:
        # Check if psutil is installed
        import psutil
        print("   ✅ psutil is available")
    except ImportError:
        print("   📦 Installing psutil...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "psutil>=5.9.0"])
        print("   ✅ psutil installed successfully")
    
    # Check other critical dependencies
    critical_deps = ["fastapi", "uvicorn", "torch", "torchaudio", "speechbrain"]
    missing_deps = []
    
    for dep in critical_deps:
        try:
            __import__(dep)
            print(f"   ✅ {dep} is available")
        except ImportError:
            missing_deps.append(dep)
            print(f"   ❌ {dep} is missing")
    
    if missing_deps:
        print(f"\n⚠️ Missing dependencies: {', '.join(missing_deps)}")
        print("Please install them using: pip install -r requirements.txt")
        return False
    
    return True

def check_gpu_availability():
    """Check GPU availability and provide recommendations"""
    print("\n🔍 Checking GPU availability...")
    
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            print(f"   ✅ CUDA available with {gpu_count} GPU(s)")
            print(f"   🎮 Primary GPU: {gpu_name}")
            
            # Check memory
            total_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            print(f"   💾 GPU Memory: {total_memory:.1f} GB")
            
            if total_memory < 4:
                print("   ⚠️ Warning: Low GPU memory. Consider using CPU for large models.")
            
            return True
        else:
            print("   ❌ CUDA not available. Using CPU.")
            print("   💡 For better performance, consider using a GPU-enabled environment.")
            return False
    except Exception as e:
        print(f"   ❌ Error checking GPU: {e}")
        return False

def start_api_server(host="0.0.0.0", port=8000, workers=1):
    """Start the FastAPI server with optimized settings"""
    print(f"\n🚀 Starting Accent Recognition API...")
    print(f"   🌐 Host: {host}")
    print(f"   🔌 Port: {port}")
    print(f"   👥 Workers: {workers}")
    
    # Prepare uvicorn command
    cmd = [
        sys.executable, "-m", "uvicorn",
        "main:app",
        "--host", host,
        "--port", str(port),
        "--workers", str(workers),
        "--log-level", "info"
    ]
    
    # Add reload for development
    if os.getenv("DEVELOPMENT", "false").lower() == "true":
        cmd.append("--reload")
        print("   🔄 Development mode: Auto-reload enabled")
    
    print(f"\n📝 Command: {' '.join(cmd)}")
    print("\n" + "="*60)
    print("🎯 API will be available at:")
    print(f"   📍 Main API: http://{host}:{port}")
    print(f"   📊 Health Check: http://{host}:{port}/health")
    print(f"   📈 Statistics: http://{host}:{port}/stats")
    print(f"   📚 API Docs: http://{host}:{port}/docs")
    print("="*60)
    
    try:
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\n\n🛑 Server stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Server failed to start: {e}")
        return False
    
    return True

def wait_for_api(url="http://localhost:8000", timeout=30):
    """Wait for API to become available"""
    print(f"\n⏳ Waiting for API to start (timeout: {timeout}s)...")
    
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            response = requests.get(f"{url}/health", timeout=5)
            if response.status_code == 200:
                print("   ✅ API is ready!")
                return True
        except requests.exceptions.RequestException:
            pass
        
        time.sleep(1)
        print("   ⏳ Still waiting...")
    
    print(f"   ❌ API failed to start within {timeout} seconds")
    return False

def show_performance_tips():
    """Show performance optimization tips"""
    print("\n💡 Performance Tips:")
    print("   🔥 Use GPU for faster inference (if available)")
    print("   💾 The API includes intelligent caching for repeated requests")
    print("   📊 Monitor performance using /stats endpoint")
    print("   🧹 Clear cache using /clear-cache endpoint if needed")
    print("   🔄 For production, consider using multiple workers")
    print("   📈 Test performance using: python performance_test.py")

def main():
    """Main startup function"""
    print("🎵 Accent Recognition API - Optimized Version")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists("main.py"):
        print("❌ main.py not found. Please run this script from the project directory.")
        sys.exit(1)
    
    # Check dependencies
    if not check_and_install_dependencies():
        print("\n❌ Dependency check failed. Please install missing packages.")
        sys.exit(1)
    
    # Check GPU
    has_gpu = check_gpu_availability()
    
    # Show performance tips
    show_performance_tips()
    
    # Get configuration from environment or use defaults
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", "8000"))
    workers = int(os.getenv("WORKERS", "1"))
    
    # Adjust workers based on GPU availability
    if not has_gpu and workers == 1:
        print("\n💡 Tip: Consider using multiple workers for CPU-only inference")
    
    print(f"\n🎬 Starting in 3 seconds...")
    time.sleep(3)
    
    # Start the server
    success = start_api_server(host, port, workers)
    
    if success:
        print("\n✅ Server started successfully!")
    else:
        print("\n❌ Failed to start server")
        sys.exit(1)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
