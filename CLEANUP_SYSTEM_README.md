# 🧹 File Cleanup System

## 🎯 **PENTING: File TIDAK Disimpan ke Disk!**

### ✅ **Sistem In-Memory Processing**
API menggunakan **in-memory processing** - file yang diupload **TIDAK disimpan ke disk**:

```python
# File langsung diproses dari memory
file_content = await file.read()  # Baca ke memory
audio_buffer = io.BytesIO(file_content_bytes)  # Process in-memory
waveform, sample_rate = torchaudio.load(audio_buffer, format="wav")
```

**Alur File:**
```
Client Upload → Memory (bytes) → Audio Processing → Response → Memory Cleared
```

**TIDAK ADA** file yang tersimpan di folder `uploads/` atau tempat lain! 🚫💾

## 🛡️ **Sistem Cleanup Preventif**

Meskipun file tidak disimpan, saya menambahkan sistem cleanup untuk:

### 1. **Membersihkan File Lama** (jika ada)
- File di folder `uploads/` yang mungkin tersisa dari versi lama
- Temporary files sistem yang terkait aplikasi
- File yang lebih tua dari 1 jam (configurable)

### 2. **Monitoring Disk Usage**
- Real-time monitoring folder uploads
- Statistik cleanup yang detail
- Status sistem cleanup

## ⚙️ **Konfigurasi Cleanup**

```python
class Config:
    # Cleanup configuration
    CLEANUP_INTERVAL = 300  # Cleanup every 5 minutes (300 seconds)
    UPLOADS_FOLDER = "uploads"  # Folder to clean
    MAX_FILE_AGE_HOURS = 1  # Delete files older than 1 hour
    ENABLE_CLEANUP = True  # Enable/disable cleanup system
```

## 🔧 **Cara Kerja Cleanup System**

### 1. **Automatic Cleanup** (Background Task)
```python
# Runs every 5 minutes
async def periodic_cleanup():
    while True:
        await asyncio.sleep(config.CLEANUP_INTERVAL)
        cleanup_old_files()
```

### 2. **Manual Cleanup** (On-Demand)
```bash
curl -X POST http://localhost:8000/cleanup-files
```

### 3. **Startup Cleanup** (Initial Clean)
```python
# Runs when API starts
cleanup_old_files()
logger.info("Initial cleanup completed")
```

## 📊 **Monitoring Endpoints**

### 1. **Cleanup Status** - `/cleanup-status`
```json
{
  "cleanup_enabled": true,
  "cleanup_interval": 300,
  "max_file_age_hours": 1,
  "uploads_folder": "uploads",
  "uploads_info": {
    "exists": true,
    "file_count": 0,
    "total_size": 0
  },
  "cleanup_stats": {
    "last_cleanup": "2024-01-01T12:00:00",
    "total_cleanups": 15,
    "files_deleted": 0,
    "bytes_freed": 0
  },
  "in_memory_processing": true,
  "note": "Files are processed in-memory, no disk storage used for uploads"
}
```

### 2. **Enhanced Stats** - `/stats`
```json
{
  "total_requests": 150,
  "average_inference_time": 1.234,
  "cache_hit_rate": 45.5,
  "memory_usage_mb": 2048.5,
  "cleanup_enabled": true,
  "cleanup_stats": {
    "last_cleanup": "2024-01-01T12:00:00",
    "total_cleanups": 15,
    "files_deleted": 0,
    "bytes_freed": 0
  }
}
```

### 3. **Manual Cleanup** - `/cleanup-files`
```json
{
  "message": "Manual cleanup completed",
  "files_deleted": 0,
  "bytes_freed": 0,
  "cleanup_stats": {
    "last_cleanup": "2024-01-01T12:00:00",
    "total_cleanups": 16,
    "files_deleted": 0,
    "bytes_freed": 0
  }
}
```

## 🧪 **Testing Cleanup System**

### 1. **Check Current Status**
```bash
curl http://localhost:8000/cleanup-status
```

### 2. **Manual Cleanup Test**
```bash
curl -X POST http://localhost:8000/cleanup-files
```

### 3. **Create Test Files** (untuk testing)
```bash
# Create test files in uploads folder
mkdir -p uploads
echo "test" > uploads/test1.txt
echo "test" > uploads/test2.txt

# Wait 1+ hours or change MAX_FILE_AGE_HOURS to 0 for immediate cleanup
```

### 4. **Monitor Cleanup**
```python
import requests
import time

# Check status before
status_before = requests.get('http://localhost:8000/cleanup-status').json()
print(f"Files before: {status_before['uploads_info']['file_count']}")

# Trigger cleanup
cleanup_result = requests.post('http://localhost:8000/cleanup-files').json()
print(f"Files deleted: {cleanup_result['files_deleted']}")

# Check status after
status_after = requests.get('http://localhost:8000/cleanup-status').json()
print(f"Files after: {status_after['uploads_info']['file_count']}")
```

## ⚡ **Performance Impact**

### 1. **Memory Usage**
- **In-Memory Processing**: Menggunakan RAM, bukan disk
- **Cleanup Task**: Minimal CPU usage (runs every 5 minutes)
- **No I/O Blocking**: Cleanup berjalan di background

### 2. **Disk Usage**
- **Zero Upload Storage**: File tidak disimpan ke disk
- **Preventive Cleanup**: Membersihkan file yang mungkin ada
- **Temp File Management**: Membersihkan temporary files sistem

## 🔧 **Customization**

### 1. **Disable Cleanup** (jika tidak diperlukan)
```python
ENABLE_CLEANUP = False
```

### 2. **Change Cleanup Interval**
```python
CLEANUP_INTERVAL = 600  # 10 minutes instead of 5
```

### 3. **Change File Age Threshold**
```python
MAX_FILE_AGE_HOURS = 24  # Keep files for 24 hours instead of 1
```

### 4. **Change Upload Folder**
```python
UPLOADS_FOLDER = "temp_uploads"  # Different folder name
```

## 🚨 **Error Handling**

### 1. **Cleanup Errors**
```python
try:
    cleanup_old_files()
except Exception as e:
    logger.error(f"Cleanup error: {e}")
    # Cleanup continues on next interval
```

### 2. **Permission Errors**
```python
try:
    file_path.unlink()
except PermissionError:
    logger.warning(f"Permission denied: {file_path}")
    # Skip file, continue with others
```

### 3. **Folder Not Found**
```python
if uploads_path.exists():
    # Only cleanup if folder exists
    cleanup_files()
```

## 📈 **Benefits**

### ✅ **Security**
- No sensitive audio files stored on disk
- Automatic cleanup of any residual files
- Reduced attack surface

### ✅ **Performance**
- Faster processing (no disk I/O for uploads)
- No disk space issues
- Better memory management

### ✅ **Maintenance**
- Self-cleaning system
- Real-time monitoring
- Manual control when needed

## 🎉 **Summary**

✅ **In-Memory Processing** - File TIDAK disimpan ke disk  
✅ **Preventive Cleanup** - Membersihkan file lama jika ada  
✅ **Background Task** - Cleanup otomatis setiap 5 menit  
✅ **Manual Control** - Trigger cleanup kapan saja  
✅ **Real-time Monitoring** - Status dan statistik lengkap  
✅ **Zero Disk Usage** - Tidak ada file upload yang tersimpan  
✅ **Configurable** - Semua setting bisa diubah  

Sistem ini memastikan **tidak ada file yang menumpuk** dan **disk tetap bersih**! 🚀
