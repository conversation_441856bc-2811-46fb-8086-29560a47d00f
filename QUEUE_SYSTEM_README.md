# 🚦 Queue System & Response Format Update

## 📋 Perubahan Utama

### 1. **Response Format Baru**
API sekarang hanya mengembalikan confidence score untuk US accent:

**Sebelum:**
```json
{
  "predicted_accent": "us",
  "us_confidence": 85.67
}
```

**Sesudah:**
```json
{
  "us_confidence": 85.67
}
```

**Jika tidak ada US accent:**
```json
{
  "us_confidence": 0.0
}
```

### 2. **Sistem Antrian (Queue System)**
API sekarang dapat menangani multiple requests secara bersamaan dengan sistem antrian:

- **Max Concurrent Requests**: 5 (dapat diubah di config)
- **Max Queue Size**: 20 requests
- **Request Timeout**: 30 detik
- **Automatic Queue Management**: Request otomatis masuk antrian jika server sibuk

## 🔧 Konfigurasi

```python
class Config:
    # Queue configuration
    MAX_CONCURRENT_REQUESTS = 5  # Maximum concurrent inference requests
    MAX_QUEUE_SIZE = 20  # Maximum requests in queue
    REQUEST_TIMEOUT = 30  # Timeout per request in seconds
```

## 🚀 Cara Kerja Queue System

### Skenario 1: Server Tidak Sibuk
```
Request → Langsung Diproses → Response
```

### Skenario 2: Server Sibuk
```
Request → Masuk Queue → Menunggu Slot → Diproses → Response
```

### Skenario 3: Queue Penuh
```
Request → HTTP 503 "Server too busy. Please try again later."
```

### Skenario 4: Timeout
```
Request → Queue → Timeout (30s) → HTTP 408 "Request timeout"
```

## 📊 Monitoring Endpoints

### 1. **Queue Status** - `/queue-status`
```json
{
  "queue_size": 3,
  "max_queue_size": 20,
  "active_requests": 5,
  "max_concurrent_requests": 5,
  "available_slots": 0,
  "queue_full": false,
  "semaphore_locked": true,
  "active_request_ids": ["req_1_1703123456", "req_2_1703123457"]
}
```

### 2. **Enhanced Stats** - `/stats`
```json
{
  "total_requests": 150,
  "average_inference_time": 1.234,
  "cache_hit_rate": 45.5,
  "memory_usage_mb": 2048.5,
  "cache_size": 25,
  "max_cache_size": 100,
  "queue_size": 3,
  "max_queue_size": 20,
  "active_requests": 5,
  "max_concurrent_requests": 5,
  "available_slots": 0
}
```

## 🎯 Response Codes

| Code | Meaning | Description |
|------|---------|-------------|
| **200** | Success | Request berhasil diproses |
| **400** | Bad Request | File tidak valid (.wav only) |
| **408** | Request Timeout | Request timeout di queue (30s) |
| **503** | Service Unavailable | Server terlalu sibuk, queue penuh |
| **500** | Internal Server Error | Error dalam processing |

## 🧪 Testing

### 1. **Basic Test**
```bash
python example_usage.py
```

### 2. **Performance Test dengan Queue**
```bash
python performance_test.py
```

### 3. **Manual Concurrent Test**
```python
import requests
import concurrent.futures

def test_request():
    with open('test.wav', 'rb') as f:
        response = requests.post('http://localhost:8000/identify', files={'file': f})
        return response.json()

# Send 10 concurrent requests
with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
    futures = [executor.submit(test_request) for _ in range(10)]
    results = [future.result() for future in futures]
```

## 📈 Performance Benefits

### 1. **Concurrent Handling**
- **Sebelum**: 1 request at a time
- **Sesudah**: Up to 5 concurrent + 20 queued

### 2. **Response Time**
- **Direct Processing**: 0.1-2s (sama seperti sebelumnya)
- **Queued Processing**: 0.1-30s (tergantung antrian)
- **Cache Hit**: ~0.01s (sangat cepat)

### 3. **Throughput**
- **Sebelum**: ~0.5-1 requests/second
- **Sesudah**: ~2-5 requests/second (tergantung hardware)

## 🔍 Monitoring Real-time

### Check Queue Status
```bash
curl http://localhost:8000/queue-status
```

### Check Performance Stats
```bash
curl http://localhost:8000/stats
```

### Health Check
```bash
curl http://localhost:8000/health
```

## ⚙️ Tuning Performance

### 1. **Increase Concurrent Requests** (untuk server yang kuat)
```python
MAX_CONCURRENT_REQUESTS = 10  # Default: 5
```

### 2. **Increase Queue Size** (untuk load tinggi)
```python
MAX_QUEUE_SIZE = 50  # Default: 20
```

### 3. **Adjust Timeout** (untuk file besar)
```python
REQUEST_TIMEOUT = 60  # Default: 30 seconds
```

## 🚨 Error Handling

### Client Side
```python
import requests

try:
    response = requests.post('http://localhost:8000/identify', files={'file': f}, timeout=35)
    
    if response.status_code == 200:
        result = response.json()
        us_confidence = result['us_confidence']
        
    elif response.status_code == 503:
        print("Server busy, try again later")
        
    elif response.status_code == 408:
        print("Request timeout, try again")
        
    else:
        print(f"Error: {response.status_code} - {response.text}")
        
except requests.exceptions.Timeout:
    print("Request timeout")
except requests.exceptions.ConnectionError:
    print("Connection error")
```

## 🎉 Summary

✅ **Response format** disederhanakan - hanya US confidence  
✅ **Queue system** untuk handling multiple requests  
✅ **Concurrent processing** up to 5 requests bersamaan  
✅ **Automatic queuing** saat server sibuk  
✅ **Real-time monitoring** queue dan performance  
✅ **Error handling** yang lebih baik  
✅ **Backward compatibility** untuk semua optimisasi sebelumnya  

API sekarang **jauh lebih robust** dan dapat menangani **traffic tinggi** dengan **response format yang clean**! 🚀
